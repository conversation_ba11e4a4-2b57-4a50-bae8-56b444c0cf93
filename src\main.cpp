#include <OneWire.h>
#include <DallasTemperature.h>

#define ONE_WIRE_BUS 15  // DS18B20 terhubung pada GPIO13
#define PH_SENSOR_PIN 2  // Sensor pH terhubung pada GPIO12
#define GAS_SENSOR_PIN 39 // Sensor Gas terhubung pada GPIO39

// Inisialisasi OneWire untuk komunikasi dengan DS18B20
OneWire oneWire(ONE_WIRE_BUS);
DallasTemperature sensors(&oneWire);

// Variabel untuk penyimpanan nilai sensor
float pHValue;
float temperature;
float gasPPM;

void setup() {
  Serial.begin(115200);  // Me<PERSON>lai komunikasi serial
  sensors.begin();  // Memulai komunikasi dengan sensor DS18B20
  Serial.println("Sensor readings initialized.");
}

void loop() {
  // Membaca data suhu dari DS18B20
  sensors.requestTemperatures();  // Meminta pembacaan suhu dari sensor
  temperature = sensors.getTempCByIndex(0);  // Mendapatkan suhu dalam Celcius
  
  // Membaca data pH dari sensor pH (analog)
  int pHReading = analogRead(PH_SENSOR_PIN);  // Membaca nilai analog dari pH sensor
  float voltage = pHReading * (3.3 / 4095.0);  // Menghitung tegangan berdasarkan nilai ADC
  pHValue = (voltage - 2.5) * 10;  // Mengonversi tegangan ke pH (perlu kalibrasi lebih lanjut)

  // Membaca data gas dari sensor MQ-8 (analog)
  int gasReading = analogRead(GAS_SENSOR_PIN);  // Membaca nilai analog dari sensor gas
  voltage = gasReading * (3.3 / 4095.0);  // Menghitung tegangan berdasarkan nilai ADC
  gasPPM = voltage * 2000;  // Mengonversi tegangan ke ppm (perlu kalibrasi lebih lanjut)

  // Menampilkan hasil pembacaan ke serial monitor
  Serial.print("pH Value: ");
  Serial.println(pHValue);  // Menampilkan nilai pH
  
  Serial.print("Temperature: ");
  Serial.print(temperature);  // Menampilkan suhu dalam Celcius
  Serial.println(" °C");
  
  Serial.print("Gas Concentration (PPM): ");
  Serial.println(gasPPM);  // Menampilkan konsentrasi gas dalam ppm
  
  delay(1000);  // Menunggu 1 detik sebelum pembacaan berikutnya
}
